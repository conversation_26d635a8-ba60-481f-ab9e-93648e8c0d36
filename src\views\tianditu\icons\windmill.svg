<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" width="100" height="100">
  <!-- 风车塔身 -->
  <rect x="47" y="40" width="6" height="55" fill="#e0e0e0" stroke="#999" stroke-width="1"/>
  
  <!-- 风车底座 -->
  <rect x="42" y="90" width="16" height="8" fill="#666" rx="2"/>
  
  <!-- 风车中心 -->
  <circle cx="50" cy="40" r="4" fill="#333"/>
  
  <!-- 风车叶片 - 使用动画旋转 -->
  <g transform-origin="50 40">
    <!-- 叶片1 -->
    <path d="M50 40 L50 15 Q52 12 55 15 Q58 18 55 25 L50 40" fill="#4a90e2" stroke="#2c5aa0" stroke-width="1"/>
    
    <!-- 叶片2 -->
    <path d="M50 40 L72 48 Q75 50 72 53 Q69 56 62 53 L50 40" fill="#4a90e2" stroke="#2c5aa0" stroke-width="1"/>
    
    <!-- 叶片3 -->
    <path d="M50 40 L28 48 Q25 50 28 53 Q31 56 38 53 L50 40" fill="#4a90e2" stroke="#2c5aa0" stroke-width="1"/>
    
    <!-- 旋转动画 -->
    <animateTransform
      attributeName="transform"
      attributeType="XML"
      type="rotate"
      from="0 50 40"
      to="360 50 40"
      dur="3s"
      repeatCount="indefinite"/>
  </g>
  
  <!-- 风车顶部装饰 -->
  <circle cx="50" cy="40" r="2" fill="#fff" stroke="#333" stroke-width="1"/>
</svg>
