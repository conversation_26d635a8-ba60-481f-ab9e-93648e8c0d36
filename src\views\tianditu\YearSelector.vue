<template>
  <div class="date-selector">
    <!-- 年份选择器 -->
    <div class="year-selector">
      <button
        class="nav-btn prev-btn"
        @click="previousYear"
        :disabled="currentYear <= minYear"
      >
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polyline points="15,18 9,12 15,6" />
        </svg>
      </button>

      <div class="years-container">
        <div class="years-list" :style="{ transform: `translateX(${translateX}px)` }">
          <button
            v-for="year in years"
            :key="year"
            class="year-btn"
            :class="{ active: year === currentYear }"
            @click="selectYear(year)"
          >
            {{ year }}
          </button>
        </div>
      </div>

      <button
        class="nav-btn next-btn"
        @click="nextYear"
        :disabled="currentYear >= maxYear"
      >
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polyline points="9,18 15,12 9,6" />
        </svg>
      </button>
    </div>

    <!-- 月份选择器 -->
    <div class="month-selector">
      <button
        class="nav-btn prev-btn"
        @click="previousMonth"
        :disabled="currentMonth <= 1 || isPlaying"
      >
        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polyline points="15,18 9,12 15,6" />
        </svg>
      </button>

      <!-- 播放控制按钮 -->
      <button
        class="play-btn"
        @click="togglePlay"
        :title="isPlaying ? '暂停播放' : '自动播放'"
      >
        <svg v-if="!isPlaying" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polygon points="5,3 19,12 5,21" />
        </svg>
        <svg v-else width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <rect x="6" y="4" width="4" height="16" />
          <rect x="14" y="4" width="4" height="16" />
        </svg>
      </button>

      <div class="months-container">
        <div class="months-list">
          <button
            v-for="month in months"
            :key="month.value"
            class="month-btn"
            :class="{ active: month.value === currentMonth }"
            @click="selectMonth(month.value)"
            :disabled="isPlaying"
          >
            {{ month.label }}
          </button>
        </div>
      </div>

      <button
        class="nav-btn next-btn"
        @click="nextMonth"
        :disabled="currentMonth >= 12 || isPlaying"
      >
        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polyline points="9,18 15,12 9,6" />
        </svg>
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onUnmounted } from 'vue'

// Props
const props = defineProps({
  currentYear: {
    type: Number,
    default: 2020
  },
  currentMonth: {
    type: Number,
    default: 1
  }
})

// Emits
const emit = defineEmits(['date-change'])

// 年份范围
const minYear = 2020
const maxYear = 2025
const visibleYears = 7 // 可见的年份数量

// 响应式数据
const translateX = ref(0)
const yearWidth = 80 // 每个年份按钮的宽度
const isPlaying = ref(false)
const playTimer = ref(null)
const playSpeed = 7000 // 播放速度，毫秒（7秒）

// 月份数据
const months = [
  { value: 1, label: '1月' },
  { value: 2, label: '2月' },
  { value: 3, label: '3月' },
  { value: 4, label: '4月' },
  { value: 5, label: '5月' },
  { value: 6, label: '6月' },
  { value: 7, label: '7月' },
  { value: 8, label: '8月' },
  { value: 9, label: '9月' },
  { value: 10, label: '10月' },
  { value: 11, label: '11月' },
  { value: 12, label: '12月' }
]

// 生成年份数组
const years = computed(() => {
  const yearArray = []
  for (let year = minYear; year <= maxYear; year++) {
    yearArray.push(year)
  }
  return yearArray
})

// 计算当前年份在数组中的索引
const currentIndex = computed(() => {
  return years.value.indexOf(props.currentYear)
})

// 更新滚动位置
const updateScrollPosition = () => {
  const containerWidth = visibleYears * yearWidth
  const totalWidth = years.value.length * yearWidth
  const maxTranslate = totalWidth - containerWidth
  
  // 计算目标位置，使当前年份居中
  const targetTranslate = (currentIndex.value - Math.floor(visibleYears / 2)) * yearWidth
  
  // 限制滚动范围
  translateX.value = -Math.max(0, Math.min(maxTranslate, targetTranslate))
}

// 选择年份
const selectYear = (year) => {
  emit('date-change', { year, month: props.currentMonth })
}

// 选择月份
const selectMonth = (month) => {
  emit('date-change', { year: props.currentYear, month })
}

// 上一年
const previousYear = () => {
  if (props.currentYear > minYear) {
    emit('date-change', { year: props.currentYear - 1, month: props.currentMonth })
  }
}

// 下一年
const nextYear = () => {
  if (props.currentYear < maxYear) {
    emit('date-change', { year: props.currentYear + 1, month: props.currentMonth })
  }
}

// 上一月
const previousMonth = () => {
  if (props.currentMonth > 1) {
    emit('date-change', { year: props.currentYear, month: props.currentMonth - 1 })
  }
}

// 下一月
const nextMonth = () => {
  if (props.currentMonth < 12) {
    emit('date-change', { year: props.currentYear, month: props.currentMonth + 1 })
  }
}

// 自动播放下一月（循环）
const autoNextMonth = () => {
  const nextMonthValue = props.currentMonth >= 12 ? 1 : props.currentMonth + 1
  emit('date-change', { year: props.currentYear, month: nextMonthValue })
}

// 开始播放
const startPlay = () => {
  if (playTimer.value) return

  isPlaying.value = true
  playTimer.value = setInterval(() => {
    autoNextMonth()
  }, playSpeed)

  console.log('开始自动播放月份')
}

// 停止播放
const stopPlay = () => {
  if (playTimer.value) {
    clearInterval(playTimer.value)
    playTimer.value = null
  }
  isPlaying.value = false
  console.log('停止自动播放')
}

// 切换播放状态
const togglePlay = () => {
  if (isPlaying.value) {
    stopPlay()
  } else {
    startPlay()
  }
}

// 监听当前年份变化，更新滚动位置
watch(() => props.currentYear, () => {
  updateScrollPosition()
}, { immediate: true })

// 组件卸载时清理定时器
onUnmounted(() => {
  stopPlay()
})
</script>

<style scoped>
.date-selector {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 2000;
  /* 确保在小屏幕上也有足够的间距 */
  margin-bottom: env(safe-area-inset-bottom, 0px);
}

.year-selector {
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 25px;
  padding: 8px 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.month-selector {
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 20px;
  padding: 6px 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  gap: 8px;
}

.nav-btn {
  background: transparent;
  border: none;
  color: white;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.nav-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.1);
}

.nav-btn:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.years-container {
  width: 560px; /* 7 * 80px */
  overflow: hidden;
  margin: 0 8px;
}

.years-list {
  display: flex;
  transition: transform 0.3s ease;
}

.year-btn {
  min-width: 80px;
  height: 36px;
  background: transparent;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  border-radius: 18px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.year-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.year-btn.active {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  font-weight: 600;
}

/* 月份选择器样式 */
.months-container {
  width: 480px; /* 12个月 * 40px */
  overflow: hidden;
  margin: 0 6px;
}

.months-list {
  display: flex;
  gap: 2px;
}

.month-btn {
  min-width: 38px;
  height: 28px;
  background: transparent;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 11px;
  font-weight: 500;
  border-radius: 14px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.month-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.month-btn.active {
  background: rgba(255, 255, 255, 0.25);
  color: #fff;
  font-weight: 600;
}

.month-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 播放按钮样式 */
.play-btn {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.play-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: scale(1.05);
}

.play-btn:active {
  transform: scale(0.95);
}

.play-btn svg {
  transition: all 0.2s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .date-selector {
    top: 10px;
  }

  .year-selector {
    padding: 6px 8px;
  }

  .month-selector {
    padding: 4px 8px;
  }

  .years-container {
    width: 400px; /* 5 * 80px */
  }

  .months-container {
    width: 360px; /* 12 * 30px */
  }

  .year-btn {
    min-width: 60px;
    height: 32px;
    font-size: 12px;
  }

  .month-btn {
    min-width: 28px;
    height: 24px;
    font-size: 10px;
  }

  .play-btn {
    width: 28px;
    height: 28px;
  }
}

@media (max-width: 480px) {
  .date-selector {
    bottom: 15px;
    /* 在移动端使用更小的底部间距 */
  }

  .year-selector {
    padding: 4px 6px;
  }

  .month-selector {
    padding: 3px 6px;
  }

  .years-container {
    width: 240px; /* 3 * 80px */
  }

  .months-container {
    width: 240px; /* 12 * 20px */
  }

  .year-btn {
    min-width: 50px;
    height: 28px;
    font-size: 11px;
  }

  .month-btn {
    min-width: 18px;
    height: 20px;
    font-size: 9px;
  }

  .play-btn {
    width: 24px;
    height: 24px;
  }
}
</style>
