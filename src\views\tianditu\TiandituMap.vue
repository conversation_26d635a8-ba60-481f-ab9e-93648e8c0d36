<template>
  <div class="map-container">
    <div id="map" ref="mapContainer"></div>

    <YearSelector
      :current-year="currentYear"
      :current-month="currentMonth"
      @date-change="handleDateChange"
    />

    <!-- 缩放级别指示器 -->
    <div class="zoom-indicator" v-if="currentZoomLevel">
      <div class="zoom-level">缩放: {{ currentZoomLevel }}</div>
      <div class="display-mode">{{ getCurrentDisplayMode() }}</div>
      <!-- *** 新增: 懒加载状态指示器 -->
      <div v-if="currentZoomLevel >= 15" class="lazy-load-status">
        <div class="load-mode">📍 分片加载模式</div>
        <div v-if="isLazyLoading" class="loading-status">🔄 加载中...</div>
        <div v-else class="loading-status">✅ 就绪</div>
      </div>
    </div>

    <!-- 图层切换按钮 -->
    <div class="layer-switch">
      <button
        :class="['layer-btn', { active: currentLayerType === 'fengdian2024' }]"
        @click="switchLayer('fengdian2024')"
      >
        <span class="layer-icon">🌪️</span>
        <span class="layer-text">风电</span>
      </button>
      <button
        :class="['layer-btn', { active: currentLayerType === 'haiyangmuchang2024' }]"
        @click="switchLayer('haiyangmuchang2024')"
      >
        <span class="layer-icon">🌊</span>
        <span class="layer-text">海洋牧场</span>
      </button>
    </div>

    <!-- 加载指示器 -->
    <div v-if="isLoading" class="loading-indicator">
      <div class="loading-spinner"></div>
      <div class="loading-text">正在加载数据...</div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'
import YearSelector from './YearSelector.vue'

// 天地图API密钥
const TIANDITU_KEY = 'c71477a3f164d6938566310e4c5df556'

// 响应式数据
const mapContainer = ref(null)
const currentYear = ref(2024)
const currentMonth = ref(1)
const currentZoomLevel = ref(0)
const isLoading = ref(false)
const currentLayerType = ref('fengdian2024') // 当前图层类型: 'fengdian2024' 或 'haiyangmuchang2024'
let map = null

// 防抖定时器
let updateDataTimer = null
const UPDATE_DELAY = 500 // 500ms 防抖延迟

// *** 新增: 分片懒加载相关变量
let isLazyLoading = ref(false) // 懒加载状态
const MIN_ZOOM_FOR_LAZY_LOADING = 15 // 启用懒加载的最小缩放级别
// *** 新增: 性能优化参数
const MAX_TOTAL_FEATURES = 5000 // 总最大要素数量

// 天地图图层URLs
const tiandituLayers = {
  // 矢量底图
  vec: `https://t{s}.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${TIANDITU_KEY}`,
  // 矢量注记
  cva: `https://t{s}.tianditu.gov.cn/cva_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${TIANDITU_KEY}`,
  // 影像底图
  img: `https://t{s}.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${TIANDITU_KEY}`,
  // 影像注记
  cia: `https://t{s}.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${TIANDITU_KEY}`
}

// GeoServer WFS 配置 - 使用代理路径
const geoServerConfig = {
  wfsUrl: '/geoserver/ne/ows', // 使用代理路径，不需要完整的 URL
  layers: {
    fengdian2024: {
      typeName: 'ne:fengdian2024',
      title: '风电2024数据',
      outputFormat: 'application/json',
      version: '1.0.0',
      crs: 'EPSG:4490', // 中国大地坐标系2000
      bounds: [
        [18.0, 108.0], // 西南角 - 扩大范围覆盖整个广东省及周边
        [26.0, 118.0]  // 东北角
      ],
      maxFeatures: 500000, // 最大获取要素数量
      geometryType: 'Point'
    },
    haiyangmuchang2024: {
      typeName: 'ne:haiyangmuchang2024',
      title: '海洋牧场2024数据',
      outputFormat: 'application/json',
      version: '1.0.0',
      crs: 'EPSG:4490', // 中国大地坐标系2000
      bounds: [
        [18.0, 108.0], // 西南角 - 扩大范围覆盖整个广东省及周边
        [26.0, 118.0]  // 东北角
      ],
      maxFeatures: 50000, // 最大获取要素数量
      geometryType: 'Polygon'
    }
  }
}

// 初始化地图
const initMap = () => {
  if (!mapContainer.value) {
    console.error('地图容器未找到')
    return
  }
  // 确保容器有明确的尺寸
  mapContainer.value.style.width = '100%'
  mapContainer.value.style.height = 'calc(100vh - 140px)'

  // 创建地图实例 - 设置更大的显示范围
  map = L.map(mapContainer.value, {
    center: [22.0, 113.5], // 广东省中心坐标
    zoom: 8, // 更大的显示范围
    minZoom: 3,
    maxZoom: 18,
    zoomControl: true,
    preferCanvas: true, // *** 修改: 优先使用Canvas渲染器以提高性能
    renderer: L.canvas()  // *** 修改: 明确使用Canvas渲染器
  })
  // 添加天地图矢量底图（首选）
  const vecLayer = L.tileLayer(tiandituLayers.vec, {
    subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
    attribution: '&copy; 天地图',
    minZoom: 3,
    maxZoom: 18,
    tileSize: 256,
    zoomOffset: 0,
    crossOrigin: true
  })

  // 添加天地图矢量注记
  const cvaLayer = L.tileLayer(tiandituLayers.cva, {
    subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
    attribution: '&copy; 天地图',
    minZoom: 3,
    maxZoom: 18,
    tileSize: 256,
    zoomOffset: 0,
    crossOrigin: true
  })

  // 添加天地图影像底图
  const imgLayer = L.tileLayer(tiandituLayers.img, {
    subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
    attribution: '&copy; 天地图',
    minZoom: 3,
    maxZoom: 18,
    tileSize: 256,
    zoomOffset: 0,
    crossOrigin: true
  })

  // 添加天地图影像注记
  const ciaLayer = L.tileLayer(tiandituLayers.cia, {
    subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
    attribution: '&copy; 天地图',
    minZoom: 3,
    maxZoom: 18,
    tileSize: 256,
    zoomOffset: 0,
    crossOrigin: true
  })

  // 备用OpenStreetMap图层
  const osmLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
    attribution: '&copy; OpenStreetMap contributors',
    minZoom: 3,
    maxZoom: 18,
    crossOrigin: true
  })

  // 组合天地图图层
  const tiandituVecGroup = L.layerGroup([vecLayer, cvaLayer])
  const tiandituImgGroup = L.layerGroup([imgLayer, ciaLayer])

  tiandituImgGroup.addTo(map)

  // 添加图层控制器
  const baseLayers = {
    "天地图影像": tiandituImgGroup,
    "天地图矢量": tiandituVecGroup,
    "OpenStreetMap": osmLayer
  }

  L.control.layers(baseLayers).addTo(map)

  // 监听图层切换事件
  map.on('baselayerchange', () => {
    // 重新计算地图尺寸以确保新图层正确显示
    setTimeout(() => {
      if (map) {
        map.invalidateSize()
      }
    }, 100)
  })

  // 监听天地图加载错误，自动切换到备用图层
  let imgErrorCount = 0
  let vecErrorCount = 0
  const maxErrors = 3

  imgLayer.on('tileerror', (e) => {
    imgErrorCount++
    console.warn(`天地图影像图层加载失败 (${imgErrorCount}/${maxErrors})`, e)

    if (imgErrorCount >= maxErrors) {
      console.warn('天地图影像加载失败次数过多，切换到矢量图层')
      map.removeLayer(tiandituImgGroup)
      tiandituVecGroup.addTo(map)
    }
  })

  vecLayer.on('tileerror', (e) => {
    vecErrorCount++
    console.warn(`天地图矢量图层加载失败 (${vecErrorCount}/${maxErrors})`, e)

    if (vecErrorCount >= maxErrors && imgErrorCount >= maxErrors) {
      console.warn('天地图所有图层加载失败，切换到OpenStreetMap')
      map.removeLayer(tiandituVecGroup)
      osmLayer.addTo(map)
    }
  })

  // 调整地图尺寸的函数
  const resizeMap = () => {
    if (map) {
      map.invalidateSize(true)
    }
  }

  // 多次尝试调整地图尺寸以确保正确布局
  setTimeout(resizeMap, 100)
  setTimeout(resizeMap, 500)

  // 地图准备好后，设置初始视图并加载数据
  map.whenReady(() => {
    resizeMap()
    const initialBounds = L.latLngBounds([
      [18.0, 108.0], // 西南角
      [26.0, 118.0]  // 东北角
    ])
    map.fitBounds(initialBounds, { padding: [20, 20] })
    loadInitialData()
  })

  // 监听缩放事件
  map.on('zoomend', () => {
    currentZoomLevel.value = map.getZoom()
    
    // *** 修改: 缩放时处理懒加载
    if (currentZoomLevel.value >= MIN_ZOOM_FOR_LAZY_LOADING) {
      // 清除低缩放级别的缓存数据
      clearLowZoomCache()
      loadCurrentViewData()
    } else {
      // 清除懒加载数据，回到全局数据模式
      clearLazyLoadedData()
      updateDataDisplay()
    }
  })

  // 监听移动事件
  map.on('moveend', () => {
    if (currentZoomLevel.value >= MIN_ZOOM_FOR_LAZY_LOADING) {
      // *** 修改: 高缩放级别时使用懒加载
      debouncedLazyLoad()
    } else {
      // 低缩放级别时使用原有逻辑
      debouncedUpdateData()
    }
  })

  // 初始化缩放级别
  currentZoomLevel.value = map.getZoom()
}

// 加载初始数据
const loadInitialData = async () => {
  await processAndDisplayData(2024, 1, false)
}

// 数据管理变量
let currentDataLayers = []
let currentGeoData = []
let aggregatedMarkers = []
// *** 新增: 懒加载相关的数据管理
let lazyLoadedLayers = [] // 懒加载的图层
let lazyLoadedData = new Map() // 按瓦片存储的懒加载数据

// 从 GeoServer WFS 获取 JSON 数据
const fetchWFSData = async (year, month, bounds = null, layerType = null) => {
  try {
    isLoading.value = true
    const layerKey = layerType || currentLayerType.value
    const layerConfig = geoServerConfig.layers[layerKey]

    // 构建 WFS 请求 URL
    const params = new URLSearchParams({
      service: 'WFS',
      version: layerConfig.version,
      request: 'GetFeature',
      typeName: layerConfig.typeName,
      outputFormat: layerConfig.outputFormat,
      maxFeatures: layerConfig.maxFeatures.toString()
    })

    if (bounds && map) {
      const bbox = `${bounds.getWest()},${bounds.getSouth()},${bounds.getEast()},${bounds.getNorth()}`
      params.append('bbox', bbox)
      params.append('srsname', 'EPSG:4326')
    }

    if (year && month) {
      const timingSque = `${year}${month.toString().padStart(2, '0')}`
      params.append('cql_filter', `TimingSequ=${timingSque}`)
    }

    const url = `${geoServerConfig.wfsUrl}?${params.toString()}`
    console.log('WFS 请求 URL:', url)

    const response = await fetch(url)

    if (!response.ok) {
      throw new Error(`HTTP 错误! 状态: ${response.status}`)
    }

    const responseText = await response.text()
    if (responseText.trim().startsWith('<?xml') || responseText.trim().startsWith('<')) {
      console.error('服务器返回XML错误:', responseText)
      throw new Error('服务器返回XML错误，请检查图层名称和参数。')
    }

    return JSON.parse(responseText)
  } catch (error) {
    console.error('获取WFS数据时出错:', error)
    throw error
  } finally {
    isLoading.value = false
  }
}

// 处理和显示 GeoJSON 数据
const processAndDisplayData = async (year, month, useCurrentBounds = false) => {
  try {
    clearDataLayers()
    let bounds = useCurrentBounds && map ? map.getBounds() : null
    const geoJsonData = await fetchWFSData(year, month, bounds)

    currentGeoData = geoJsonData && geoJsonData.features ? geoJsonData.features : []
    updateDataDisplay()

  } catch (error) {
    currentGeoData = []
  }
}

// 清除所有数据图层
const clearDataLayers = () => {
  currentDataLayers.forEach(layer => map.removeLayer(layer))
  currentDataLayers = []
  aggregatedMarkers.forEach(marker => map.removeLayer(marker))
  aggregatedMarkers = []
}

// *** 修改: 加载当前视窗数据（从缓存数据中筛选）
const loadCurrentViewData = async () => {
  if (!map || isLazyLoading.value) return
  
  const currentBounds = map.getBounds()
  const zoom = map.getZoom()
  
  // 如果没有缓存数据，先加载全部数据
  if (!currentGeoData.length) {
    console.log('没有缓存数据，先加载全部数据')
    isLazyLoading.value = true
    try {
      await processAndDisplayData(currentYear.value, currentMonth.value, false)
    } finally {
      isLazyLoading.value = false
    }
    
    if (!currentGeoData.length) {
      console.log('无数据可显示')
      return
    }
  }
  
  console.log(`分片模式: 从 ${currentGeoData.length} 个缓存要素中筛选当前视窗数据`)
  
  // 根据缩放级别调整瓦片大小
  const currentTileSize = getOptimalTileSize(zoom)
  
  // 获取当前视窗范围内的要素
  const viewportFeatures = filterFeaturesInBounds(currentGeoData, currentBounds)
  
  console.log(`视窗内找到 ${viewportFeatures.length} 个要素`)
  
  // 更新懒加载显示
  updateLazyLoadedDisplayFromCache(viewportFeatures)
}

// *** 新增: 从缓存数据中筛选指定边界内的要素
const filterFeaturesInBounds = (features, bounds) => {
  return features.filter(feature => {
    const { geometry } = feature
    
    if (geometry.type === 'Point') {
      const [lng, lat] = geometry.coordinates
      return bounds.contains([lat, lng])
    } else if (geometry.type === 'Polygon' || geometry.type === 'MultiPolygon') {
      // 对于多边形，检查其中心点是否在边界内
      try {
        const geoJsonLayer = L.geoJSON(feature)
        const center = geoJsonLayer.getBounds().getCenter()
        return bounds.contains(center)
      } catch (error) {
        console.warn('多边形处理失败:', error)
        return false
      }
    }
    
    return false
  })
}

// *** 新增: 根据缩放级别获取最佳瓦片大小（用于性能优化参考）
const getOptimalTileSize = (zoom) => {
  if (zoom >= 18) return 0.005  // ~500m
  if (zoom >= 17) return 0.01   // ~1km
  if (zoom >= 16) return 0.02   // ~2km
  if (zoom >= 15) return 0.05   // ~5km
  return 0.1 // ~10km
}

// *** 修改: 更新懒加载显示（从缓存数据）
const updateLazyLoadedDisplayFromCache = (features) => {
  if (!map) return
  
  // 清除现有的懒加载图层
  clearLazyLoadedLayers()
  
  const totalFeatures = features.length
  console.log(`准备渲染 ${totalFeatures} 个视窗内要素`)
  
  // 性能优化 - 限制渲染数量
  const maxFeaturesToRender = Math.min(totalFeatures, MAX_TOTAL_FEATURES)
  const featuresToRender = features.slice(0, maxFeaturesToRender)
  
  if (totalFeatures > MAX_TOTAL_FEATURES) {
    console.warn(`要素过多 (${totalFeatures})，只渲染前 ${MAX_TOTAL_FEATURES} 个`)
  }
  
  // 渲染要素
  const layers = renderTileFeatures(featuresToRender)
  lazyLoadedLayers = layers
  
  console.log(`分片懒加载: 成功渲染 ${featuresToRender.length} 个要素`)
}

// *** 新增: 渲染瓦片要素
const renderTileFeatures = (features) => {
  const layers = []
  
  // *** 修改: 移除重复的限制逻辑，由调用方控制要素数量
  features.forEach(feature => {
    const { geometry, properties } = feature
    let layer = null

    if (geometry.type === 'Point') {
      const [lng, lat] = geometry.coordinates
      layer = L.marker([lat, lng], {
        icon: createWindTurbineIcon(30)
      }).addTo(map)
    } else if (geometry.type === 'Polygon' || geometry.type === 'MultiPolygon') {
      layer = L.geoJSON(feature, {
        style: {
          fillColor: getPolygonColor(properties),
          color: '#2196F3',
          weight: 1,
          opacity: 0.8,
          fillOpacity: 0.3
        }
      }).addTo(map)
    }

    if (layer) {
      layer.bindPopup(createDetailedFeaturePopup(properties))
      layers.push(layer)
    }
  })
  
  return layers
}

// *** 新增: 清除懒加载图层
const clearLazyLoadedLayers = () => {
  lazyLoadedLayers.forEach(layer => {
    if (map.hasLayer(layer)) {
      map.removeLayer(layer)
    }
  })
  lazyLoadedLayers = []
}

// *** 修改: 清除懒加载数据
const clearLazyLoadedData = () => {
  clearLazyLoadedLayers()
  // 不清除 currentGeoData，保持缓存
}

// *** 删除: 移除不再需要的瓦片相关变量和函数
// let loadedTiles = new Map()
// let lazyLoadedData = new Map()
// const MAX_CACHED_TILES = 50
// getTileBounds, cleanupTileCache 等函数

// *** 新增: 清除低缩放级别缓存
const clearLowZoomCache = () => {
  // 在进入高缩放级别时，清除原有的全局数据显示
  clearDataLayers()
}

// *** 新增: 懒加载防抖函数
const debouncedLazyLoad = () => {
  clearTimeout(updateDataTimer)
  updateDataTimer = setTimeout(loadCurrentViewData, UPDATE_DELAY)
}

// 切换图层类型
const switchLayer = async (layerType) => {
  if (layerType === currentLayerType.value) return
  console.log(`切换图层: ${currentLayerType.value} -> ${layerType}`)

  // 清除所有现有数据和显示
  clearDataLayers()
  clearLazyLoadedData()
  currentGeoData = [] // *** 修改: 切换图层时清空缓存，重新获取数据
  currentLayerType.value = layerType

  // 重新获取新图层的数据
  await processAndDisplayData(currentYear.value, currentMonth.value, false)
  
  // 如果当前是高缩放级别，立即更新懒加载显示
  if (currentZoomLevel.value >= MIN_ZOOM_FOR_LAZY_LOADING) {
    loadCurrentViewData()
  }
}

// 防抖更新函数（用于地图移动）
const debouncedUpdateData = () => {
  clearTimeout(updateDataTimer)
  updateDataTimer = setTimeout(updateDataDisplay, UPDATE_DELAY)
}

// 根据缩放级别更新数据显示（来自缓存数据）
const updateDataDisplay = () => {
  if (!map) {
    clearDataLayers()
    return
  }

  const zoomLevel = map.getZoom()
  
  // *** 修改: 高缩放级别时不处理全局数据显示
  if (zoomLevel >= MIN_ZOOM_FOR_LAZY_LOADING) {
    console.log('高缩放级别，使用懒加载模式')
    return // 懒加载模式下不使用此函数
  }
  
  if (!currentGeoData.length) {
    clearDataLayers()
    return
  }

  clearDataLayers()
  console.log(`更新显示: 缩放=${zoomLevel}, 数据量=${currentGeoData.length}`)

  if (zoomLevel >= 13 && currentGeoData.length <= 20) {
    console.log('显示详细要素（图标/多边形）')
    showDetailedFeatures()
  } else {
    console.log('显示聚合数据（数字标记）')
    showAggregatedData(zoomLevel)
  }
}

// 显示聚合数据（数字标记）
const showAggregatedData = (zoomLevel) => {
  const aggregatedData = aggregateFeaturesByZoomLevel(currentGeoData, zoomLevel)

  aggregatedData.forEach(group => {
    const marker = L.marker([group.lat, group.lng], {
      icon: createAggregatedIcon(group, zoomLevel)
    }).addTo(map)

    marker.on('click', () => {
      const nextZoom = Math.min(zoomLevel + 2, 18)
      map.setView([group.lat, group.lng], nextZoom)
    })

    marker.bindPopup(`
      <div class="feature-popup">
        <h4>聚合区域</h4>
        <p><strong>要素数量:</strong> ${group.count}</p>
        <p><strong>级别:</strong> ${getLevelName(zoomLevel)}</p>
        <p><em>点击放大查看</em></p>
      </div>
    `)
    aggregatedMarkers.push(marker)
  })
}

// 显示详细要素（点和多边形）
const showDetailedFeatures = () => {
  currentGeoData.forEach(feature => {
    const { geometry, properties } = feature
    let layer = null

    if (geometry.type === 'Point') {
      const [lng, lat] = geometry.coordinates
      layer = L.marker([lat, lng], {
        icon: createWindTurbineIcon(50)
      }).addTo(map)
    } else if (geometry.type === 'Polygon' || geometry.type === 'MultiPolygon') {
      layer = L.geoJSON(feature, {
        style: {
          fillColor: getPolygonColor(properties),
          color: '#2196F3',
          weight: 2,
          opacity: 0.8,
          fillOpacity: 0.4
        }
      }).addTo(map)
    }

    if (layer) {
      layer.bindPopup(createDetailedFeaturePopup(properties))
      currentDataLayers.push(layer)
    }
  })
}

// 按缩放级别聚合要素
const aggregateFeaturesByZoomLevel = (features, zoomLevel) => {
  const gridSize = getGridSizeByZoomLevel(zoomLevel)
  const groups = new Map()

  features.forEach(feature => {
    let lat, lng
    if (feature.geometry.type === 'Point') {
      [lng, lat] = feature.geometry.coordinates
    } else if (['Polygon', 'MultiPolygon'].includes(feature.geometry.type)) {
      const center = L.geoJSON(feature).getBounds().getCenter()
      lat = center.lat
      lng = center.lng
    } else {
      return
    }

    const gridLat = Math.floor(lat / gridSize) * gridSize
    const gridLng = Math.floor(lng / gridSize) * gridSize
    const key = `${gridLat}_${gridLng}`

    if (!groups.has(key)) {
      groups.set(key, { lat: 0, lng: 0, count: 0, totalLat: 0, totalLng: 0 })
    }

    const group = groups.get(key)
    group.count++
    group.totalLat += lat
    group.totalLng += lng
  })

  return Array.from(groups.values()).map(g => ({
    count: g.count,
    lat: g.totalLat / g.count,
    lng: g.totalLng / g.count
  }))
}

// 处理窗口大小调整
const handleResize = () => {
  if (map) {
    setTimeout(() => map.invalidateSize(), 100)
  }
}

// 创建详细要素弹窗内容
const createDetailedFeaturePopup = (properties) => {
  let content = `<div class="feature-popup detailed-popup"><h3>${properties.EntityName || '要素详情'}</h3>`
  for (const key in properties) {
    if (properties[key] !== null && properties[key] !== '') {
      content += `<p><strong>${getFieldDisplayName(key)}:</strong> ${properties[key]}</p>`
    }
  }
  return content + '</div>'
}

// 获取字段的显示名称
const getFieldDisplayName = (fieldName) => {
  const names = {
    'ClassName': '设备类型', 'ModelID': '模型ID', 'TimingSequ': '时间序列',
    'Capacity': '容量', 'Height': '塔高', 'Status': '状态'
  }
  return names[fieldName] || fieldName
}

// 获取多边形的颜色
const getPolygonColor = (properties) => {
  const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']
  const id = properties.id || properties.ID || properties.FID || 0
  return colors[id % colors.length]
}

// 创建风车图标
const createWindTurbineIcon = (size = 24) => {
  return L.icon({
    iconUrl: new URL('./icons/windmill.svg', import.meta.url).href,
    iconSize: [size, size],
    iconAnchor: [size / 2, size / 2],
    popupAnchor: [0, -size / 2]
  })
}

// 创建聚合图标（显示数字）
const createAggregatedIcon = (group, zoomLevel) => {
  const size = getSizeByZoomLevel(zoomLevel)
  const color = getColorByZoomLevel(zoomLevel)
  return L.divIcon({
    className: 'aggregated-marker',
    html: `<div class="aggregated-marker-content" style="background-color: ${color}; width: ${size}px; height: ${size}px;">${group.count}</div>`,
    iconSize: [size, size],
    iconAnchor: [size / 2, size / 2]
  })
}

// 根据缩放级别获取图标大小
const getSizeByZoomLevel = (zoom) => {
  if (zoom <= 6) return 50; if (zoom <= 8) return 45; if (zoom <= 10) return 40;
  if (zoom <= 12) return 35; if (zoom <= 14) return 30; return 25;
}

// 根据缩放级别获取颜色
const getColorByZoomLevel = (zoom) => {
  if (zoom <= 6) return '#e74c3c'; if (zoom <= 8) return '#f39c12'; if (zoom <= 10) return '#f1c40f';
  if (zoom <= 12) return '#2ecc71'; if (zoom <= 14) return '#3498db'; return '#9b59b6';
}

// 根据缩放级别获取聚合的网格大小
const getGridSizeByZoomLevel = (zoom) => {
  if (zoom <= 6) return 2.0; if (zoom <= 8) return 1.0; if (zoom <= 10) return 0.5;
  if (zoom <= 12) return 0.2; if (zoom <= 14) return 0.1; return 0.05;
}

// 根据缩放获取级别名称
const getLevelName = (zoom) => {
  if (zoom <= 6) return '省级聚合'; if (zoom <= 8) return '市级聚合';
  if (zoom <= 10) return '区县级聚合'; if (zoom <= 12) return '镇级聚合';
  return '详细视图';
}

// 获取当前显示模式的描述
const getCurrentDisplayMode = () => {
  const zoom = currentZoomLevel.value
  const layerTitle = geoServerConfig.layers[currentLayerType.value]?.title || '数据'
  
  // *** 修改: 支持懒加载模式显示
  if (zoom >= MIN_ZOOM_FOR_LAZY_LOADING) {
    return `${layerTitle} - 分片懒加载`
  }
  
  if (zoom >= 13 && currentGeoData.length <= 20) {
    return `${layerTitle} - 详细视图`
  }
  return `${layerTitle} - ${getLevelName(zoom)}`
}

// 处理日期选择器的日期变化
const handleDateChange = async ({ year, month }) => {
  currentYear.value = year
  currentMonth.value = month
  
  // 清除现有显示
  clearLazyLoadedData()
  
  // *** 修改: 日期变化时清空缓存，重新获取对应日期的数据
  currentGeoData = []
  
  // 重新获取新日期的数据
  await processAndDisplayData(year, month, false)
  
  // 如果当前是高缩放级别，立即更新懒加载显示
  if (currentZoomLevel.value >= MIN_ZOOM_FOR_LAZY_LOADING) {
    loadCurrentViewData()
  }
}

// 组件生命周期钩子
onMounted(() => {
  initMap()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  clearTimeout(updateDataTimer)
  clearLazyLoadedData() // *** 新增: 清理懒加载数据
  if (map) {
    map.remove()
    map = null
  }
})
</script>

<style scoped>
.map-container {
  position: relative;
  width: 100%;
  height: calc(100vh - 140px);
  overflow: hidden;
}

#map {
  width: 100% !important;
  height: 100% !important;
  position: relative;
  background: #f0f0f0;
}

:deep(.leaflet-container) {
  font-size: 12px;
}

/* 缩放指示器样式 */
.zoom-indicator {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 12px;
  z-index: 2000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.zoom-level {
  font-weight: bold;
  margin-bottom: 2px;
}

.display-mode {
  font-size: 10px;
  opacity: 0.8;
}

/* *** 新增: 懒加载状态指示器样式 */
.lazy-load-status {
  margin-top: 4px;
  padding-top: 4px;
  border-top: 1px solid rgba(255, 255, 255, 0.3);
}

.load-mode {
  font-size: 10px;
  color: #4CAF50;
  font-weight: bold;
}

.loading-status {
  font-size: 9px;
  opacity: 0.9;
  margin-top: 2px;
}

/* 加载指示器样式 */
.loading-indicator {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 20px;
  border-radius: 8px;
  z-index: 3000;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
}

/* 聚合标记样式 */
:deep(.aggregated-marker) {
  background: transparent !important;
  border: none !important;
}

:deep(.aggregated-marker-content) {
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 14px;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.3);
  transition: all 0.3s ease;
}

:deep(.aggregated-marker-content:hover) {
  transform: scale(1.1);
}

/* 弹窗样式 */
:deep(.leaflet-popup-content) {
  margin: 8px 12px;
  line-height: 1.4;
}

:deep(.feature-popup) {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  max-width: 300px;
}

:deep(.feature-popup h3) {
  margin: 0 0 10px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c5aa0;
}

:deep(.feature-popup p) {
  margin: 4px 0;
  font-size: 12px;
}

:deep(.feature-popup strong) {
  font-weight: 600;
}

/* 图层切换按钮样式 */
.layer-switch {
  position: fixed;
  top: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  z-index: 1000;
}

.layer-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid #e0e0e0;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
  color: #666;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.layer-btn:hover {
  border-color: #2196F3;
  transform: translateY(-2px);
}

.layer-btn.active {
  background: #2196F3;
  border-color: #2196F3;
  color: white;
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
}

.layer-icon {
  font-size: 16px;
}

.layer-text {
  font-size: 13px;
  font-weight: 600;
}
</style>
